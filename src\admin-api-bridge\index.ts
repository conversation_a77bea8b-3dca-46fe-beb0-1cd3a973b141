import { Context } from "koa";
import jwt from "jsonwebtoken";

async function getAdminUser(ctx: Context) {
  const authHeader = ctx.request.header.authorization;

  if (!authHeader) return ctx.unauthorized("Missing Authorization header");

  const token = authHeader.replace("Bearer ", "");

  try {
    const decoded = jwt.verify(token, process.env.ADMIN_JWT_SECRET || "");

    const adminUser = await strapi.db.query("admin::user").findOne({
      where: { id: decoded["id"] },
      populate: ["roles"],
    });

    if (!adminUser) return ctx.unauthorized("Invalid admin user");
    return adminUser;
  } catch (err: any) {
    return ctx.unauthorized(`Invalid token: ${err.message}`);
  }
}

async function checkAdminPermissions(moduleId: string) {
  return async (ctx: any, next: any) => {
    const admin = await getAdminUser(ctx);
    if (!admin) return ctx.unauthorized("Invalid admin");
    ctx.state.admin = admin;

    console.log("ADMIN ::::::::: middlewares trigger ::::::::::::::: ");

    const method = ctx.request.method.toUpperCase();
    const requiredPermission = (() => {
      switch (method) {
        case "GET":
          return "canRead";
        case "POST":
          return "canCreate";
        case "PUT":
        case "PATCH":
          return "canUpdate";
        case "DELETE":
          return "canDelete";
        default:
          return null;
      }
    })();

    if (!requiredPermission) {
      return ctx.forbidden("Unsupported HTTP method");
    }

    const isSuperAdmin = ctx.state.admin.roles.some(
      (role: any) => role.code === "strapi-super-admin"
    );
    if (isSuperAdmin) return await next();

    // const hasPermission = ctx.state.admin.roles.some((role: any) =>
    //   role.admin_role_permissions?.some(
    //     (perm: any) => perm.moduleId === moduleId && perm[requiredPermission]
    //   )
    // );

    // if (!hasPermission) {
    //   return ctx.forbidden(
    //     `No ${requiredPermission} permission for module ${moduleId}`
    //   );
    // }

    return await next();
  };
}

function findManyDocument(uid: string) {
  return async (ctx: Context) => {
    try {
      const {
        locale,
        status,
        filters,
        fields,
        populate,
        pagination = {},
        sort,
      }: any = ctx.query;
      const { page = 1, pageSize = 25 } = pagination as any;

      const start = (Number(page) - 1) * Number(pageSize);
      const limit = Number(pageSize);

      // @ts-ignore
      const doc: any = strapi.documents(uid);

      const data = await doc.findMany({
        locale,
        status,
        filters,
        fields,
        populate,
        sort,
        start,
        limit,
      });

      const total = await doc.count({ filters });

      return ctx.send({
        data,
        meta: {
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
          },
        },
      });
    } catch (err) {
      console.log("Bridge Error", err);
      return ctx.internalServerError("Bridge failed");
    }
  };
}

function findOneDocument(uid: string) {
  return async (ctx: Context) => {
    try {
      const { id } = ctx.params;
      const { locale, status, populate, fields }: any = ctx.query;

      let docId = id;

      if (/^\d+$/.test(id)) {
        const { documentId } = await strapi
          .query(uid)
          .findOne({ where: { id } });
        docId = documentId;
      }

      // @ts-ignore
      const doc: any = strapi.documents(uid);
      const data = await doc.findOne({
        documentId: docId,
        locale,
        status,
        fields,
        populate,
      });

      return ctx.send({
        data,
        meta: {},
      });
    } catch (err) {
      console.log("Bridge Error", err);
      return ctx.internalServerError("Bridge failed");
    }
  };
}

export { checkAdminPermissions, findManyDocument, findOneDocument };
