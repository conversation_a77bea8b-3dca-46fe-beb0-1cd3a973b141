import { Core } from "@strapi/strapi";
import { Context } from "koa";

import { startCronJob } from "./api/scheduler/helpers";
import { ResetBusinessPartnerStagingState } from "./api/business-partner-staging/sync";
import { ResetProductStagingState } from "./api/product-staging/sync";
import { ResetBPContactStagingState } from "./api/bp-contact-staging/sync";
import { ResetProductHierarchyStagingState } from "./api/product-hierarchy-staging/sync";
import {
  checkAdminPermissions,
  findManyDocument,
  findOneDocument,
} from "./admin-api-bridge";

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  async register({ strapi }: { strapi: Core.Strapi }) {
    const AdminBridgeRoutes = [];

    // -----------------------------
    // ✅ Core API routes
    // -----------------------------
    // for (const apiName in strapi.apis) {
    //   const api: any = strapi.api(apiName);
    //   let ctrl: any = api.controller(apiName);
    //   const contentType = api.contentType(apiName);
    //   const uid = contentType?.uid;

    //   if (uid && uid.includes("api::")) {
    //     ctrl.find = findManyDocument(uid);
    //     ctrl.findOne = findOneDocument(uid);
    //   }

    //   const routeGroups = [
    //     api.routes?.[apiName],
    //     api.routes?.[`custom-${apiName}`],
    //   ];

    //   for (const routeGroup of routeGroups) {
    //     if (!routeGroup || !Array.isArray(routeGroup.routes)) continue;

    //     for (const route of routeGroup.routes) {
    //       const handler: any = route.handler;
    //       if (typeof handler !== "string") continue;

    //       const method = handler.split(".").pop();

    //       AdminBridgeRoutes.push({
    //         method: route.method,
    //         path: `/api-bridge${route.path}`,
    //         handler: async (ctx: Context) => {
    //           return ctrl[method](ctx);
    //         },
    //         config: {
    //           auth: false,
    //           middlewares: [await checkAdminPermissions(apiName)],
    //         },
    //       });
    //     }
    //   }
    // }

    // -----------------------------
    // ✅ Plugin API routes
    // -----------------------------
    // for (const pluginName in strapi.plugins) {
    //   const plugin = strapi.plugin(pluginName);

    //   if (!["users-permissions"].includes(pluginName)) continue;

    //   const contentAPI = plugin.routes?.["content-api"];

    //   if (!contentAPI?.routes || !Array.isArray(contentAPI.routes)) continue;

    //   for (const route of contentAPI.routes) {
    //     const handler: any = route.handler;
    //     if (typeof handler !== "string") continue;

    //     const [controllerName, method] = handler.split(".");

    //     let path: string = `/api-bridge${route.path}`;

    //     if (controllerName === "role") {
    //       path = `/api-bridge/users-permissions${route.path}`;
    //     }

    //     AdminBridgeRoutes.push({
    //       method: route.method,
    //       path,
    //       handler: async (ctx: Context) => {
    //         const ctrl: any = plugin.controller(controllerName);
    //         return ctrl[method](ctx);
    //       },
    //       config: {
    //         auth: false,
    //         middlewares: [await checkAdminPermissions(pluginName)],
    //       },
    //     });
    //   }
    // }

    // -----------------------------
    // ✅ Register admin bridge routes
    // -----------------------------
    strapi.server.routes(AdminBridgeRoutes);
  },
  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  async bootstrap({ strapi }: { strapi: Core.Strapi }) {
    strapi.db.lifecycles.subscribe({
      models: ["plugin::upload.file"],
      async afterCreate(event) {
        const getFileType = (mimeType) => {
          if (mimeType.startsWith("image/")) return "IMAGE";
          if (mimeType.startsWith("video/")) return "VIDEO";
          if (mimeType === "application/pdf") return "PDF";
          return mimeType;
        };
        const { result } = event;
        if (result.name) {
          const fileType = getFileType(result.mime);
          const nameWithoutExtension = result.name.split(".")[0];
          const [first, second, third] = nameWithoutExtension.split("_");
          if (fileType) {
            if (first.toLowerCase() == "cat") {
              await strapi
                .query("api::product-category.product-category")
                .update({
                  where: { category_id: second },
                  data: {
                    url: result.url,
                  },
                });
            } else {
              const product = await strapi
                .query(`api::product.product`)
                .findOne({
                  where: {
                    product_id: first,
                  },
                });
              if (product) {
                let filePath = nameWithoutExtension;
                // Extract folder IDs from folderPath and get folder names
                if (result.folderPath && result.folderPath !== "/") {
                  try {
                    // Split folderPath to get all folder IDs (e.g., "/1/2/3" -> ["1", "2", "3"])
                    const folderIds = result.folderPath
                      .split("/")
                      .filter((id: string) => id !== "");

                    // Fetch only the needed folders based on folderIds
                    const neededFolders = await strapi
                      .query("plugin::upload.folder")
                      .findMany({
                        where: {
                          pathId: {
                            $in: folderIds.map((id: string) => parseInt(id)),
                          },
                        },
                        select: ["id", "name", "pathId"],
                      });

                    // Create a map for quick folder lookup by ID
                    const folderMap = new Map();
                    neededFolders.forEach((folder: any) => {
                      folderMap.set(folder.pathId, folder);
                    });

                    // Build the folder path using the folder IDs from folderPath
                    const folderNames = [];
                    for (const folderId of folderIds) {
                      const folder = folderMap.get(parseInt(folderId));
                      if (folder) {
                        folderNames.push(folder.name);
                      }
                    }

                    const folderPath = folderNames.join("/");
                    filePath = folderPath
                      ? `${folderPath}/${nameWithoutExtension}`
                      : nameWithoutExtension;
                  } catch (error) {
                    console.error("Error fetching folder details:", error);
                  }
                }

                await strapi
                  .documents(`api::product-media.product-media`)
                  .create({
                    data: {
                      product_id: first,
                      file_path: "/" + filePath,
                      code: (second || "").toString(),
                      url: result.url,
                      media_type: fileType,
                      is_cover_image: second == 1 ? true : false,
                    },
                  });
              }
            }
          }
        }
      },
    });

    // Load all active schedulers and start their cron jobs on application start
    const schedulers = await strapi.db
      .query("api::scheduler.scheduler")
      .findMany({
        where: {
          is_active: true,
        },
      });

    for (const scheduler of schedulers) {
      await startCronJob(scheduler);
    }

    // Reset Staging State for sync data
    ResetBusinessPartnerStagingState();
    ResetBPContactStagingState();
    ResetProductStagingState();
    ResetProductHierarchyStagingState();
  },
};
