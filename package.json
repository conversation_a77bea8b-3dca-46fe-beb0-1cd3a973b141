{"name": "chs", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "cross-env NODE_OPTIONS=--max_old_space_size=8192 strapi build", "deploy": "strapi deploy", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "build:upload-plugin": "npm pack ./custom-providers/strapi-provider-cloudflare-r2-custom --pack-destination ."}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^5.0.1", "@azure/storage-blob": "^12.27.0", "@json2csv/node": "^7.0.6", "@strapi/plugin-cloud": "5.18.1", "@strapi/plugin-users-permissions": "5.18.1", "@strapi/provider-email-nodemailer": "^5.6.0", "@strapi/provider-upload-aws-s3": "^5.6.0", "@strapi/strapi": "5.18.1", "archiver": "^7.0.1", "aws-sdk": "^2.1287.0", "axios": "^1.7.8", "bcryptjs": "^3.0.2", "country-state-city": "^3.2.1", "cross-env": "^7.0.3", "csv-parser": "^3.1.0", "csv-stringify": "^6.5.2", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-email-designer-5": "^0.0.7", "strapi-plugin-media-library-handler": "^0.1.6", "strapi-provider-cloudflare-r2-custom": "file:strapi-provider-cloudflare-r2-custom-1.0.1.tgz", "strapi-v5-plugin-populate-deep": "^4.0.4", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}